// WXS 工具：将部分纯展示层逻辑迁移到视图层，减少 JS 负担
var SWITCH_WORKING = 'working'
var SWITCH_OVERTIME = 'overtime'
var SWITCH_FISHING = 'fishing'

function switchMode(isFishing, segmentType) {
  if (isFishing) {
    return SWITCH_FISHING
  }
  if (segmentType == 'overtime') {
    return SWITCH_OVERTIME
  }
  return SWITCH_WORKING
}

function leftActive(isFishing) {
  return !isFishing
}

function rightActive(isFishing) {
  return !!isFishing
}

function workDescText(segmentType) {
  return segmentType == 'overtime' ? '努力加班中' : '专注工作中'
}

function leftOptionText(segmentType) {
  return segmentType == 'overtime' ? '加班' : '工作'
}

function shouldShowExtraIncome(value) {
  return Number(value || 0) > 0
}

function shouldShowDeductions(value) {
  return Number(value || 0) > 0
}

module.exports = {
  switchMode: switchMode,
  leftActive: leftActive,
  rightActive: rightActive,
  workDescText: workDescText,
  leftOptionText: leftOptionText,
  shouldShowExtraIncome: shouldShowExtraIncome,
  shouldShowDeductions: shouldShowDeductions
}


