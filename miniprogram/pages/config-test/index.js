/**
 * 配置测试页面
 * 用于测试和管理云端配置功能
 */

// 导入API模块
import { api } from '../../core/api/index.js'

Page({
  data: {
    configs: {},
    isLoading: false,
    logs: []
  },

  onLoad() {
    console.log('[ConfigTest] 配置测试页面加载')
    this.loadConfigs()
  },

  /**
   * 加载配置数据
   */
  async loadConfigs() {
    this.setData({ isLoading: true })
    this.addLog('开始加载配置数据...')

    try {
      const result = await api.config.getAllConfigs()

      if (result && result.success) {
        const configs = result.data || {}

        // 处理配置数据，确保对象类型的配置能正确显示
        const processedConfigs = {}
        Object.keys(configs).forEach(key => {
          const value = configs[key]
          if (typeof value === 'object' && value !== null) {
            // 对象类型配置，添加标识字段
            processedConfigs[key] = {
              ...value,
              _isObject: true
            }
          } else {
            // 简单类型配置
            processedConfigs[key] = value
          }
        })

        this.setData({
          configs: processedConfigs,
          isLoading: false
        })
        this.addLog(`配置加载成功，共 ${Object.keys(configs).length} 个配置`)
      } else {
        throw new Error(result?.message || '获取配置失败')
      }
    } catch (error) {
      console.error('[ConfigTest] 加载配置失败:', error)
      this.addLog(`配置加载失败: ${error.message}`)
      this.setData({ isLoading: false })
      
      wx.showToast({
        title: '加载配置失败',
        icon: 'none'
      })
    }
  },



  /**
   * 测试统计页面访问控制
   */
  async onTestStatisticsAccess() {
    this.addLog('测试统计页面访问控制...')

    try {
      const configManager = getApp().getConfigManager()
      
      if (!configManager.isLoaded()) {
        this.addLog('配置管理器未加载，重新加载...')
        await configManager.reload()
      }

      const access = configManager.checkPageAccess('statistics')
      this.addLog(`统计页面访问检查结果: enabled=${access.enabled}, title="${access.title}", message="${access.message}"`)

      if (access.enabled) {
        wx.navigateTo({
          url: '/pages/statistics/index'
        })
      } else {
        wx.showModal({
          title: access.title || '页面访问受限',
          content: access.message,
          showCancel: false
        })
      }
    } catch (error) {
      console.error('[ConfigTest] 测试访问控制失败:', error)
      this.addLog(`测试访问控制失败: ${error.message}`)
    }
  },

  /**
   * 切换统计页面访问状态（提示手动修改）
   */
  async onToggleStatisticsAccess() {
    const currentConfig = this.data.configs['statistics_page'] || { enabled: true, title: '即将上线', message: '我们正在努力完善这个功能' }

    this.addLog(`当前统计页面配置: enabled=${currentConfig.enabled}`)

    wx.showModal({
      title: '配置修改提示',
      content: `当前统计页面访问状态: ${currentConfig.enabled ? '允许' : '禁止'}\n\n请在云函数控制台的数据库中手动修改 config 集合中 key 为 'statistics_page' 的记录来测试功能。`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 清空日志
   */
  onClearLogs() {
    this.setData({ logs: [] })
  },

  /**
   * 添加日志
   */
  addLog(message) {
    const timestamp = new Date().toLocaleTimeString()
    const logs = this.data.logs.slice()
    logs.unshift(`[${timestamp}] ${message}`)
    
    // 只保留最近50条日志
    if (logs.length > 50) {
      logs.splice(50)
    }
    
    this.setData({ logs })
    console.log(`[ConfigTest] ${message}`)
  }
})
