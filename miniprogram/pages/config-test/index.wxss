/* 配置测试页面样式 */
.config-test-page {
  padding: 32rpx;
  background: #f8fafc;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 48rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
}

/* 操作按钮 */
.actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 48rpx;
}

.action-btn {
  background: #ffffff;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 32rpx;
  color: #374151;
  text-align: center;
}

.action-btn.primary {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.action-btn.warning {
  background: #f59e0b;
  border-color: #f59e0b;
  color: #ffffff;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn[disabled] {
  opacity: 0.5;
}

/* 配置区域 */
.config-section, .logs-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.clear-btn {
  background: #ef4444;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

/* 配置列表 */
.config-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  border: 1rpx solid #e5e7eb;
}

.config-key {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  flex: 1;
  margin-right: 24rpx;
}

.config-value-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.config-value {
  font-size: 28rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.config-value.enabled {
  background: #dcfce7;
  color: #166534;
}

.config-value.disabled {
  background: #fee2e2;
  color: #dc2626;
}

.config-value.normal {
  background: #f3f4f6;
  color: #374151;
}

/* 对象类型配置样式 */
.config-object {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  align-items: flex-end;
}

.config-sub-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.sub-key {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 400;
}

.sub-value {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-weight: 500;
}

.sub-value.enabled {
  background: #dcfce7;
  color: #166534;
}

.sub-value.disabled {
  background: #fee2e2;
  color: #dc2626;
}

.sub-value.normal {
  background: #f3f4f6;
  color: #374151;
}

/* 日志列表 */
.logs-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.log-item {
  font-size: 24rpx;
  color: #6b7280;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
  font-family: monospace;
  line-height: 1.4;
}

.log-item:last-child {
  border-bottom: none;
}

/* 空状态 */
.empty-state {
  text-align: center;
  color: #9ca3af;
  font-size: 28rpx;
  padding: 48rpx 0;
}
